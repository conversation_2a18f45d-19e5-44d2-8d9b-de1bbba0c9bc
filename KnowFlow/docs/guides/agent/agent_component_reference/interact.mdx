---
sidebar_position: 3
slug: /interact_component
---

# Interact component

A component that accepts user inputs and displays responses.

---

An **Interact** component serves as the interface between human and bot, receiving user inputs and displaying the agent's responses.


## Scenarios

An **Interact** component is essential where you need to display the agent's responses or require user-computer interaction.

## Examples

You can explore our three-step interpreter agent template, where the **Interact** component is used to display the final translation, or our customer service agent template, where the **Interact** component is the immediate downstream of **Begin** and is used to display multi-turn dialogue between the user and the agent.