---
sidebar_position: 7
slug: /message_component
---

# Message component

A component that sends out a static message.

---

A **Message** component sends out a static message. If multiple messages are supplied, it randomly selects one to send.

## Configurations

### Messages

The message to send out. 

Click **+ Add message** to add message options. When multiple messages are supplied, the **Message** component randomly selects one to send.

## Examples

Explore our customer service agent template, where the **Message** component (component ID: **What else?**) randomly sends out a message to the user interface if the user inputs is related to personal contact information:

1. Click the **Agent** tab at the top center of the page to access the **Agent** page.
2. Click **+ Create agent** on the top right of the page to open the **agent template** page.
3. On the **agent template** page, hover over the **Customer service** card and click **Use this template**.
4. Name your new agent and click **OK** to enter the workflow editor.
5. Click on the **Message** component to display its **Configuration** window.