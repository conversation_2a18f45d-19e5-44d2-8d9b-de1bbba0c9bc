---
sidebar_position: 11
slug: /upgrade_ragflow
---

# Upgrading
import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

Upgrade RAGFlow to `nightly-slim`/`nightly` or the latest, published release.

:::info NOTE
Upgrading RAGFlow in itself will *not* remove your uploaded/historical data. However, be aware that `docker compose -f docker/docker-compose.yml down -v` will remove Docker container volumes, resulting in data loss.
:::

## Upgrade RAGFlow to `nightly-slim`/`nightly`, the most recent, tested Docker image

`nightly-slim` refers to the RAGFlow Docker image *without* embedding models, while `nightly` refers to the RAGFlow Docker image with embedding models. For details on their differences, see [ragflow/docker/.env](https://github.com/infiniflow/ragflow/blob/main/docker/.env).

To upgrade RAGFlow, you must upgrade **both** your code **and** your Docker image:

1. Clone the repo

   ```bash
   git clone https://github.com/infiniflow/ragflow.git
   ```

2. Update **ragflow/docker/.env**:

<Tabs
  defaultValue="nightly-slim"
  values={[
    {label: 'nightly-slim', value: 'nightly-slim'},
    {label: 'nightly', value: 'nightly'},
  ]}>
  <TabItem value="nightly-slim">

```bash
RAGFLOW_IMAGE=infiniflow/ragflow:nightly-slim
```

  </TabItem>
  <TabItem value="nightly">

```bash
RAGFLOW_IMAGE=infiniflow/ragflow:nightly
```

  </TabItem>
</Tabs>

3. Update RAGFlow image and restart RAGFlow:

   ```bash
   docker compose -f docker/docker-compose.yml pull
   docker compose -f docker/docker-compose.yml up -d
   ```

## Upgrade RAGFlow to the most recent, officially published release

To upgrade RAGFlow, you must upgrade **both** your code **and** your Docker image:

1. Clone the repo

   ```bash
   git clone https://github.com/infiniflow/ragflow.git
   ```

2. Switch to the latest, officially published release, e.g., `v0.19.0`:

   ```bash
   git checkout -f v0.19.0
   ```

3. Update **ragflow/docker/.env** as follows:

   ```bash
   RAGFLOW_IMAGE=infiniflow/ragflow:v0.19.0
   ```

4. Update the RAGFlow image and restart RAGFlow:

   ```bash
   docker compose -f docker/docker-compose.yml pull
   docker compose -f docker/docker-compose.yml up -d
   ```

## Frequently asked questions

### Upgrade RAGFlow in an offline environment (without Internet access)

1. From an environment with Internet access, pull the required Docker image.
2. Save the Docker image to a **.tar** file.
   ```bash
   docker save -o ragflow.v0.19.0.tar infiniflow/ragflow:v0.19.0
   ```
3. Copy the **.tar** file to the target server.
4. Load the **.tar** file into Docker:
   ```bash
   docker load -i ragflow.v0.19.0.tar
   ```
