server {
    listen 80;
    server_name _;
    root /ragflow/web/dist;

    gzip on;
    gzip_min_length 1k;
    gzip_comp_level 9;
    gzip_types text/plain application/javascript application/x-javascript text/css application/xml text/javascript application/x-httpd-php image/jpeg image/gif image/png;
    gzip_vary on;
    gzip_disable "MSIE [1-6]\.";

    location ^~ /api/v1/ {
        proxy_pass http://knowflow-backend:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        include proxy.conf;
    }

    # 所有 /v1/... 路径（但不包括 /api/v1/...，因为上面已优先匹配）
    location ~ ^/v1/ {
        proxy_pass http://ragflow:9380;
        include proxy.conf;
    }

    location /minio/ {
    # 代理到 MinIO 服务（Docker 环境下通常是容器名:9000）
        proxy_pass http://minio:9000/;

        # 传递原始请求头
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 处理 CORS 跨域
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';

        # 去掉 /minio 前缀，重写路径
        rewrite ^/minio/(.*)$ /$1 break;
    }

    location / {
        index index.html;
        try_files $uri $uri/ /index.html;
    }

    # Cache-Control: max-age~@~AExpires
    location ~ ^/static/(css|js|media)/ {
        expires 10y;
        access_log off;
    }
}
