{"id": 5, "title": "Text To SQL", "description": "An agent that converts user queries into SQL statements. You must prepare three knowledge bases: 1: DDL for your database; 2: Examples of user queries converted to SQL statements; 3: A comprehensive description of your database, including but not limited to tables and records.", "canvas_type": "chatbot", "dsl": {"answer": [], "components": {"Answer:SocialAdsWonder": {"downstream": ["Retrieval:TrueCornersJam", "Retrieval:EasyDryersShop", "Retrieval:LazyChefsWatch"], "obj": {"component_name": "Answer", "inputs": [], "output": null, "params": {"debug_inputs": [], "inputs": [], "message_history_window_size": 22, "output": null, "output_var_name": "output", "post_answers": [], "query": []}}, "upstream": ["begin", "Generate:CurlyFalconsWorry"]}, "Generate:CurlyFalconsWorry": {"downstream": ["Answer:SocialAdsWonder"], "obj": {"component_name": "Generate", "inputs": [], "output": null, "params": {"cite": false, "debug_inputs": [], "frequency_penalty": 0.7, "inputs": [], "llm_id": "deepseek-chat@DeepSeek", "max_tokens": 0, "message_history_window_size": 1, "output": null, "output_var_name": "output", "parameters": [], "presence_penalty": 0.4, "prompt": "##The user provides a question and you provide SQL. You will only respond with SQL code and not with any explanations.\n\n##Respond with only SQL code. Do not answer with any explanations -- just the code.\n\n##You may use the following DDL statements as a reference for what tables might be available. Use responses to past questions also to guide you: {Retrieval:TrueCornersJam}.\n\n##You may use the following documentation as a reference for what tables might be available. Use responses to past questions also to guide you: {Retrieval:LazyChefsWatch}.\n\n##You may use the following SQL statements as a reference for what tables might be available. Use responses to past questions also to guide you: {Retrieval:EasyDryersShop}.\n\n", "query": [], "temperature": 0.1, "top_p": 0.3}}, "upstream": ["Retrieval:LazyChefsWatch", "Retrieval:EasyDryersShop", "Retrieval:TrueCornersJam"]}, "Retrieval:EasyDryersShop": {"downstream": ["Generate:CurlyFalconsWorry"], "obj": {"component_name": "Retrieval", "inputs": [], "output": null, "params": {"debug_inputs": [], "empty_response": "Nothing found in Q-SQL!", "inputs": [], "kb_ids": [], "keywords_similarity_weight": 0.3, "message_history_window_size": 22, "output": null, "output_var_name": "output", "query": [], "rerank_id": "", "similarity_threshold": 0.2, "top_k": 1024, "top_n": 8}}, "upstream": ["Answer:SocialAdsWonder"]}, "Retrieval:LazyChefsWatch": {"downstream": ["Generate:CurlyFalconsWorry"], "obj": {"component_name": "Retrieval", "inputs": [], "output": null, "params": {"debug_inputs": [], "empty_response": "Nothing found in DB-Description!", "inputs": [], "kb_ids": [], "keywords_similarity_weight": 0.3, "message_history_window_size": 22, "output": null, "output_var_name": "output", "query": [], "rerank_id": "", "similarity_threshold": 0.2, "top_k": 1024, "top_n": 8}}, "upstream": ["Answer:SocialAdsWonder"]}, "Retrieval:TrueCornersJam": {"downstream": ["Generate:CurlyFalconsWorry"], "obj": {"component_name": "Retrieval", "inputs": [], "output": null, "params": {"debug_inputs": [], "empty_response": "Nothing found in DDL!", "inputs": [], "kb_ids": [], "keywords_similarity_weight": 0.3, "message_history_window_size": 22, "output": null, "output_var_name": "output", "query": [], "rerank_id": "", "similarity_threshold": 0.02, "top_k": 1024, "top_n": 8}}, "upstream": ["Answer:SocialAdsWonder"]}, "begin": {"downstream": ["Answer:SocialAdsWonder"], "obj": {"component_name": "<PERSON><PERSON>", "inputs": [], "output": null, "params": {"debug_inputs": [], "inputs": [], "message_history_window_size": 22, "output": null, "output_var_name": "output", "prologue": "Hi! I'm your smart assistant. What can I do for you?", "query": []}}, "upstream": []}}, "embed_id": "", "graph": {"edges": [{"id": "reactflow__edge-begin-Answer:SocialAdsWonderc", "markerEnd": "logo", "source": "begin", "sourceHandle": null, "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Answer:SocialAdsWonder", "targetHandle": "c", "type": "buttonEdge"}, {"id": "reactflow__edge-Answer:SocialAdsWonderb-Retrieval:TrueCornersJamc", "markerEnd": "logo", "source": "Answer:SocialAdsWonder", "sourceHandle": "b", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Retrieval:TrueCornersJam", "targetHandle": "c", "type": "buttonEdge"}, {"id": "reactflow__edge-Answer:SocialAdsWonderb-Retrieval:EasyDryersShopc", "markerEnd": "logo", "source": "Answer:SocialAdsWonder", "sourceHandle": "b", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Retrieval:EasyDryersShop", "targetHandle": "c", "type": "buttonEdge"}, {"id": "reactflow__edge-Answer:SocialAdsWonderb-Retrieval:LazyChefsWatchc", "markerEnd": "logo", "source": "Answer:SocialAdsWonder", "sourceHandle": "b", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Retrieval:LazyChefsWatch", "targetHandle": "c", "type": "buttonEdge"}, {"id": "xy-edge__Retrieval:LazyChefsWatchb-Generate:CurlyFalconsWorryb", "markerEnd": "logo", "source": "Retrieval:LazyChefsWatch", "sourceHandle": "b", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Generate:CurlyFalconsWorry", "targetHandle": "b", "type": "buttonEdge", "zIndex": 1001}, {"id": "xy-edge__Retrieval:EasyDryersShopb-Generate:CurlyFalconsWorryb", "markerEnd": "logo", "source": "Retrieval:EasyDryersShop", "sourceHandle": "b", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Generate:CurlyFalconsWorry", "targetHandle": "b", "type": "buttonEdge", "zIndex": 1001}, {"id": "xy-edge__Retrieval:TrueCornersJamb-Generate:CurlyFalconsWorryb", "markerEnd": "logo", "source": "Retrieval:TrueCornersJam", "sourceHandle": "b", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Generate:CurlyFalconsWorry", "targetHandle": "b", "type": "buttonEdge", "zIndex": 1001}, {"id": "xy-edge__Generate:CurlyFalconsWorryc-Answer:SocialAdsWonderc", "markerEnd": "logo", "source": "Generate:CurlyFalconsWorry", "sourceHandle": "c", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Answer:SocialAdsWonder", "targetHandle": "c", "type": "buttonEdge", "zIndex": 1001}], "nodes": [{"data": {"label": "<PERSON><PERSON>", "name": "begin"}, "dragging": false, "height": 44, "id": "begin", "measured": {"height": 44, "width": 100}, "position": {"x": -520.486587527275, "y": 117.87988995940702}, "positionAbsolute": {"x": -520.486587527275, "y": 117.87988995940702}, "selected": false, "sourcePosition": "left", "targetPosition": "right", "type": "beginNode"}, {"data": {"form": {}, "label": "Answer", "name": "interface"}, "dragging": false, "height": 44, "id": "Answer:SocialAdsWonder", "measured": {"height": 44, "width": 200}, "position": {"x": -237.69220760465112, "y": 119.9282206409824}, "positionAbsolute": {"x": -284.9289105495367, "y": 119.9282206409824}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "logicNode", "width": 200}, {"data": {"form": {"empty_response": "Nothing found in DDL!", "kb_ids": [], "keywords_similarity_weight": 0.3, "similarity_threshold": 0.02, "top_n": 8}, "label": "Retrieval", "name": "DDL"}, "dragging": false, "height": 44, "id": "Retrieval:TrueCornersJam", "measured": {"height": 44, "width": 200}, "position": {"x": 119.61927071085717, "y": -40.184181873335746}, "positionAbsolute": {"x": 119.61927071085717, "y": -40.184181873335746}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "retrievalNode", "width": 200}, {"data": {"form": {"empty_response": "Nothing found in Q-SQL!", "kb_ids": [], "keywords_similarity_weight": 0.3, "similarity_threshold": 0.2, "top_n": 8}, "label": "Retrieval", "name": "Q->SQL"}, "dragging": false, "height": 44, "id": "Retrieval:EasyDryersShop", "measured": {"height": 44, "width": 200}, "position": {"x": 80.07777425685605, "y": 120.03075150115158}, "positionAbsolute": {"x": 81.2024576603057, "y": 94.16303322180948}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "retrievalNode", "width": 200}, {"data": {"form": {"empty_response": "Nothing found in DB-Description!", "kb_ids": [], "keywords_similarity_weight": 0.3, "similarity_threshold": 0.2, "top_n": 8}, "label": "Retrieval", "name": "DB Description"}, "dragging": false, "height": 44, "id": "Retrieval:LazyChefsWatch", "measured": {"height": 44, "width": 200}, "position": {"x": 51.228157704293324, "y": 252.77721891325103}, "positionAbsolute": {"x": 51.228157704293324, "y": 252.77721891325103}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "retrievalNode", "width": 200}, {"data": {"form": {"text": "Receives a sentence that the user wants to convert into SQL and displays the result of the large model's SQL conversion."}, "label": "Note", "name": "N: Interface"}, "dragHandle": ".note-drag-handle", "dragging": false, "height": 132, "id": "Note:GentleRabbitsWonder", "measured": {"height": 132, "width": 324}, "position": {"x": -287.3066094433631, "y": -30.808189185380513}, "positionAbsolute": {"x": -287.3066094433631, "y": -30.808189185380513}, "resizing": false, "selected": false, "sourcePosition": "right", "style": {"height": 132, "width": 324}, "targetPosition": "left", "type": "noteNode", "width": 324}, {"data": {"form": {"text": "The large model learns which tables may be available based on the responses from three knowledge bases and converts the user's input into SQL statements."}, "label": "Note", "name": "N: LLM"}, "dragHandle": ".note-drag-handle", "dragging": false, "height": 163, "id": "Note:SixCitiesJoke", "measured": {"height": 163, "width": 334}, "position": {"x": 19.243366453487255, "y": 531.9336820600888}, "positionAbsolute": {"x": 5.12121582244032, "y": 637.6539219843564}, "resizing": false, "selected": false, "sourcePosition": "right", "style": {"height": 147, "width": 326}, "targetPosition": "left", "type": "noteNode", "width": 334}, {"data": {"form": {"text": "Searches for description about meanings of  tables and fields."}, "label": "Note", "name": "N: DB description"}, "dragHandle": ".note-drag-handle", "dragging": false, "height": 128, "id": "Note:FamousCarpetsTaste", "measured": {"height": 128, "width": 269}, "position": {"x": 399.9267065852242, "y": 250.0329701879931}, "positionAbsolute": {"x": 399.9267065852242, "y": 250.0329701879931}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "noteNode", "width": 269}, {"data": {"form": {"text": "Searches for samples about question to SQL.\nPlease check this dataset: https://huggingface.co/datasets/InfiniFlow/text2sql"}, "label": "Note", "name": "N: Q->SQL"}, "dragHandle": ".note-drag-handle", "dragging": false, "height": 140, "id": "Note:PoliteBeesArrive", "measured": {"height": 140, "width": 455}, "position": {"x": 491.0393427986917, "y": 96.58232093146341}, "positionAbsolute": {"x": 489.0393427986917, "y": 96.58232093146341}, "resizing": false, "selected": false, "sourcePosition": "right", "style": {"height": 130, "width": 451}, "targetPosition": "left", "type": "noteNode", "width": 455}, {"data": {"form": {"text": "DDL(Data Definition Language).\n\nSearches for relevant database creation statements.\n\nIt should bind with a KB to which DDL is dumped in.\nYou could use  'General' as parsing method and ';' as delimiter."}, "label": "Note", "name": "N: DDL"}, "dragHandle": ".note-drag-handle", "dragging": false, "height": 272, "id": "Note:SmartWingsDouble", "measured": {"height": 272, "width": 288}, "position": {"x": 406.6930553966363, "y": -208.84980249039137}, "positionAbsolute": {"x": 404.1930553966363, "y": -208.84980249039137}, "resizing": false, "selected": false, "sourcePosition": "right", "style": {"height": 258, "width": 283}, "targetPosition": "left", "type": "noteNode", "width": 288}, {"data": {"form": {"cite": false, "frequencyPenaltyEnabled": true, "frequency_penalty": 0.7, "llm_id": "deepseek-chat@DeepSeek", "maxTokensEnabled": false, "max_tokens": 256, "message_history_window_size": 1, "parameter": "Precise", "parameters": [], "presencePenaltyEnabled": true, "presence_penalty": 0.4, "prompt": "##The user provides a question and you provide SQL. You will only respond with SQL code and not with any explanations.\n\n##Respond with only SQL code. Do not answer with any explanations -- just the code.\n\n##You may use the following DDL statements as a reference for what tables might be available. Use responses to past questions also to guide you: {Retrieval:TrueCornersJam}.\n\n##You may use the following documentation as a reference for what tables might be available. Use responses to past questions also to guide you: {Retrieval:LazyChefsWatch}.\n\n##You may use the following SQL statements as a reference for what tables might be available. Use responses to past questions also to guide you: {Retrieval:EasyDryersShop}.\n\n", "temperature": 0.1, "temperatureEnabled": true, "topPEnabled": true, "top_p": 0.3}, "label": "Generate", "name": "GenSQL"}, "dragging": false, "id": "Generate:CurlyFalconsWorry", "measured": {"height": 106, "width": 200}, "position": {"x": 10.728415797190792, "y": 410.2569651241076}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "generateNode"}]}, "history": [], "messages": [], "path": [], "reference": []}, "avatar": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJAAAACoCAYAAAAPb2d4AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsAAAA7AAWrWiQkAADagSURBVHhe7X0JnCVVfe5396Xv7dvLdM++sMywC4ygQUBAcInECFES8SkuRH8viT/3l2di8tREE6MmGpcszyxqXoy4IAhiEhWQRQUGEJBhRrZZYJjpmd5v37Wq7vu+c6q6b/d0z/RM3zu9cL/uc6vq1Kmz/M93/ud/TlWdCtUIEP6mhRYmIRQK+XvTI+xvW2jhqNAiUAtzQotALcwJLQK1MCe0CNTCnNAiUAtzQotALcwJLQK1MCe0CNTCnNAiUAtzQotALcwJLQK1MCcc05upodb92mOK2qHvg84KC+JmqojTIs/SRKsLa2FOaBqBAq3T0jxLGy0N1MKc0CJQC3NC0wikEUDgWli6aGmgFuaEY0KgliZauliiE4mzSGgqq2dL8KlRm+voGWwnQe3zmBX6IDSi0R5uInFpEag+fjOHICFaJdsAWc4OdXmQSE2ZpyVX87FkCNRc4kwpoA7HvVy6GstWhuOOwnXzcJ0SSqUKPKeCqrsPXq1CfweuV4XnOQxXRc1z6W8zLQHKhcNhbsOIhCOIRKJ0EcQiXXQ5RGIRJJNtDJPh+XZEolleGdXVJo5xKMpxWTRVKAYtAhmwACGX4p4w10Ie/XQ4qWx5VMsjKJboys9hNP8sSpX9KJcOwCkPouoMkRx5OCROjbwKUzAeiVWrebyWGTSZtK7GAJ5nMx0QSLBhA4QZR4znIgiFQ4glUvRJIRrpQCLRhVC8C+nkcrRn1iCd6kUy3olEsp0R0tVnnMnUPI/xBOWz6dowwf7RYVETaK5axxTexMEdRsZqNLsWVTjVPuTH9mBs7BkMDe9EobiL5NmHsjOAaq1AAriIsGKMHqAQwqxkKwu7VdQmzolI6zDVz4b2MzQOUm08pORXI+kkRo9EY9IE0yUxwuE4EjESKNaFRNs6ZHLHIZddgyzJFY+tYrCUicNEHziTtYCw5sDuTsUh6m3pECjYP5ICqfQKHzRMZwSjYzvRP7gV+fyjGBp6nNplgJVVguMV2W2QKiJJOOqThY4VavPRAEkeBkpBmsgQSbLkv5E9M1CrsVskqVzH4ZbZDMfon0QslkN7+ynIZk9GZ+50dHQcj3Ck08RnZaYfORORPA7GIerteUWg8cIyrNEMhjguNcx27N17N/r2P4SRkadoq4zQ/qggGlXrZvfBvxr1TA0RhvdMUqxHG8eCAftMadGa7XdrJp/qlml7uS4qDssRaUdbai26O0/H6uW/hs6O01iQnL2chTLtycjRCGhCYM97Aukc/WtU1w4JYbocGrP7+u7GM8/9iN3TwyiX+2lbAHEasuEQycJrjEDVuhWnib/OhqDNNHG8EKGuaUIYJvshh3YaXYUNI5ZBJnMC1q64DGtXXUojvZOddmCWq2wSmF++5z2BBBmsEQqEFX9g4H48/eT1ODB4L49HEYsmEJGxyiCkjwlO2jCqyZHVJ+GF643dxQbpJ2olt0LNBHRmXoC1a15FdxlFlKOdxTLWF/15QaDDgX2O6xzA9ie+jJ27v0/ClEmcFMnASGpUPZSEtM2kwhjpTF+4xU2gOlDLuG4VVWrk9uy5OOOUdyGb2xicnNjMIOtFTaB6HEwmeTBjKiGVSrm6Ew888ikc6L8DqWgbCZS259kWZc8I5or6shyCQOoOFz/YaGzNcNdFscKBAtbj3M3vQ0f3+ai5Do12ndbPTHLwd+aAwxHI9gnzAlYyU3e9Idz7wOfYdd2HVKKDGU6bgosENUrQ7jO4ttzMxi0NqOx++WsxyoaNKvwEHnjw08iPbqM8NGiY/9IeEwKNk6Ae0hL02/Pc7RjIb0EqmeAhRyNGe0wVjI7lHzhCzXNJaJpZQBOpLK669aKzC9ufvp7aR8b0NLPdxxjzpIFUaNuC+gYeRSxSZUZ0q0DD3eD8kUAE8wl1WFL5YadxsrnCpiu3Tr8LB2Ea0CnEYykMDG3laG2AfvNLHmHeurDg1kQtXGGluRSFRlpxbqWFZDRbd3iwmse1UVD500H+ChM4pRk4z2yNHwlkjHc6kckc81D7Ec001/3NnFajQVn5w3aNPmOa3Q5J+9D7cDI6SPU3Fk0hkLJ8+MqX8ENY1n4yPJfC8Fh5dcQxLrCgbYy+kzCD/amYXKk2FI9FAk0VuNqKDCE6tmjTtzI+TyM9EpgkroWjTFuz2REzsxBhPiKqMJ5VXFHGJafbJGFGKWfjDMhmCTcBHUzyOArYsjIrFJOHro6TEdGMNdOdnNZkKMfTSamRaPgoTFmuVKoYLY6hu6PDzluMC3GiOLZhsN27A9hy359iaPR+JFMafU2E06/uLx0EVbqRnPQAU2QNquJ0F13XaN5Ie3qUI0QiiAyaiZTe0T0yJeGxNorsMj3ry2NueI3mYDzG7dHGCJmbpzzBzGoaQWQhvcyfUk6SdAmmpTv3uucVYTjte66LCMlq8s7LTd50f0xMU8PQ1QxvRG4KaTfTQzdadTaMqlPmfhdefM5fIJM5g9fVRaBf5tESinFrn9e5DKMUFWbmNGaGTXtmNIVA+aqLm+/8Ga685MWIKwOM2hZCdk8AVQF96V0qPsnRxecxXPg5SaSQNKiplbQ3QT7+MqwqWrcETI+lY6oJl11gnNrKkz4lUdhISQ5glGQo81yV27xXQYXbqluF4zjcc1AOleHoMQ7D8rAhjv0TVMGBTGw+TX7kZQ5CSFFTxZhohGkmIwkkvDii0SjikSi6nDgS0mYkbxviiFdJMhLLc8uM3+UfK9dPTxrR/qlQ9VBiHK5HPJRKmotejbPPfAd6ey6Xt1V/vGocIXnSj8SWon5g97NY2ZnDikzGNmTLriPCPBDItu53fOY/8KZXn49LTl2tqmJCKdNC62GKrx8W1qkOYOu2f8Yz+35EjZFHihVR8xLcN7rEhDP9PQUhDePFIqjGwijWqnQOhrwiRkiVEaZVKRdIliJKoRJKYQqV10SN4mEVMV5pC0nU9JDSDIybnZpyYyrcQmHsnplXMnlnTign46jJwpqw9Iuklu657OpIaFEhzlN6hkjkSvOvM9SOHKmUicaQZtQ5dpfRirpBpU9KsUvUHLStL6VDR4J7JHi1kkA2cxZOO/1adOTOlKlm4ac9vjXamgeMfz8b8Uev+z4+dMXLsDZLApmy+AU6AhxTApkYWNOyGF73V7egb6SEWz9xJUXFFl+jTeEbggYKrLxxq6TNtAa7lL59d2EHh6lDow/T32WL9uhi1DBJuCTMGFvjALXJPmcQo7UCRitFEqiCMonisKI1cR0XOViBitiJqF2HqSmUNe2zekkgVbN+/RpjUF1gs2Vg8hcIL/CdEKbZ0zXcCbShNKy6DoXXn8f8S9fJX1GZER4rORWKY1k0jWwogVyiG93RNqQr1LtUnboPqIfadBuj6laQTq7CulWvxYYNv4WweVDNRD89TD6YHhvBh2/dgu/94mnc/u4rGb81uAMcSV3PA4HUAEJ429/dja/851N4/28fj79+0/k8wcplAFWgdEqM+0HWgpRNXuW8UezdtwW7nrkJ+fw2DEer2Is8+moDGKZmGQ0V4dSKjINazU2ypVdYR7ZZjhdXGoMC1SbIl3a08XeZnghtA9gqF+pIfkjY0BMgMZWYTyDBxmh/Tfeh0449rjK/LrVrzY0g42XRFUmhI5JEp5PCGuqnVHglVq5+Gdasuhjx5LqJ5ILMBxBhqPmM8qGaDdNK+87WJ/DmG+7CKb0r8cO3XoquxUIgAyaoJK/90k/wlXvZjCpDeP/lK/HxN50ny8a0SqOjKOzps6ZWLc2g/RIKhX24Z9fNuPu572HH6EP0GUI0yZERtVKSrTjqafREO4YaaKIElgSHK5MVDp3RTEdKoJmgWIJ0uTWEslB+ZL+p/B7tF5faxquye3eoab12dMXX4czui3DJ8a9Dd9uJiMZ6bHclksh+nE5gVH+SKHUfD6K4buvjeN/378FearaXtEVx0zWXoDMmArF8yg7LfCR1fcwJFCR47ZfuwL//vIRkJoWxkb248twcPvfWC7CqQyMtpme1v0WQtPHQCXnwwGgIC9ctYM/IY3hy4Kd4eugh7Bzchv7KEyiG+43xHInSgI1wxMVrNAwXPU2ZJhVrsjAaRyDFEySkre+M1lFXxthp46hrcmjAh2nkxl3aRKFerM+djBM6z8KJy16M5Z2nIhvr5bXKE/817WCyo3imyVddcaoM89mfP4q/uesxlOMdHBzE8IJ0BTe99RISM2ZkIXc4QkzFvBHo7V/8Cb5+j4tMNkHPCgb7h3DSChd/ds1ZeP3mE00YI+egn7HDK9afWlu9yvXzpfPjg7gaCpU9eHb0l3h2YBv2FnZgh55SHHsWVW8YpdoYR1/USrSXmCHEOCqScRtEJeNWadlRiahGsnHPzAv5tWJtGQvTNY2DcRr4pGMwPT8traKy6+lDxy1RMUSMf5iapo3aJVHrRCbVi9XZTVjffgZ6MsdjTW4TetLsnuA/0mqg+BmPymsqnAkofclVHAqypa3PqYf3D+PPb7sfNz8xiFSuFwmWu1B0cUbGw41vuQjdi5VA37jHQzabNKMw1f5AuUx+DOGNZ3Xhj15/Jk5d023CGj1tHtvQPn/GJxADqPATFTfxAPoEHNpH+coBDOX3ob/Yh/7Cs+gbfRoD1T3Iu/0oVwZJujEzbHdDeboiKqwcYzuYsit+Cpi1oqM6/pgs2XkVlY/nWbma4wmzGw1xRCitF63F0R7OIZHIoC3Wgd7QOnRm1qA9swI9qbXoTK9GOwnUFvE1TB1s2SZXrihpjHMDGefKgzijoboZcWCgWMbf378N//eBndhXjSObSop65opStUYNVMMN17wUyxKWQEeD+SMQu7Bv/EwaKEnlwUKHZKeE4TpZlIYH2ceP4LdfshzXXroJZ6xjX29AIqk/moZA1s2Egwk1ARdlL49ylZqJBCp7ZRTL+1FwSSingDEO+Wsc/ei1Hw2Z9baGmSWqqauZyEeEw0RNH8Ri7QwTQyqeNG9aRDmCSiXiSITSaIt20S+NFA1ivaHB8aC9OAC7JMtMW5ZJio2YubLkP3Fud7mEGx9+El+9fyd+MegikVmGtEYlZrrEhitVPZzBLNxwzQUkUHwREuiLJNA9DjK0gTwagTVj5FLNezSlZfg5VZRGB9CRcvGqM3vwO5ecgItO7+UQN2auF9QKrU6wRxrBCXYwbvfoXbflD410s8ehrOajdK0Ne+xg7DtDlPp8ycmLfjxn69mWT/s61OjV+lmYIH7phV+yq7rhsV341rZn8ehInmSWtmtjeNl8FRJcZbfhpYHOSJJAbxGBqIGkzSap1dlhnglUIYEyVgPJYuC5SfYEjyuOh9F8AdnwGF50fAqveeFavOKs1ThhXQfb70RYU34jXNFQ4O8MWVZRzJXTlV3XBP7B9dOFqwfDGfH44aezZ4M46sUoLx1OjX7CvmKFa+5K1NHsuubJpswuP10o4Gc7n8NNj+zCz3fnsZtdVZIaJZWMIcKwMrQFTXba7steu8QIpNxrKl6SPzhD0khOlV0N1a7HLqU3VcYZx7fhgjN6celpK3DquuXImKFoHZTfqX1As6Ck/F3BpKqfwHO22QjCma1kIo0hx4GGf1JHO/qHcdeufty2sw9bnj2AnQUH1VAcbemEGRB4rjX8JxNZVwo2HhHodEZ7w1vOX/oE0gytujk3opubFIUTRmmsRIPbQUesjI3L09i8cQXOOSVLe6kbJyxvQy4uoU+BimBLox+7mZRc/UFQXvkF+zOgPk6F166Kom2wfxCCeP00tfF361FmGXePlLF9337cu7cfW3bvx/b+InYVKYd4GzKJFO0rva1CgkQqmjM0k48hdVuTCCHJTUAEOi3hGRuoJ0kbaLETKEQjerrRk2Du+0gAhmi8nscaaruUluu5qFbKdFWKjEPiVAUrlgOnr23H2SsyOH39CmxYlsTy7hy6U9OQahx+ZdrScpcVopGfqVUd64S6Ex2rO7HlMNBQzRwqjO9fL9jxc8H5unM+dGaMxesfzuPZ0SIe6xvEtv4xbGW39PRYHnvKRRQYKBZPIUoDPar373Ud05ZMg9GV6f5moXlLNA1OT7q44c0XoDeZMEWwMSh3tpSzwaIg0ExQnqzjAa/VkNlhn1+qluFwRFVzq4hHI2hPR7AiB2zoDGNdZwpr1vRifW8KKzIJdOQy6OyIoycR4uhILVfV0njINsuzmQ+T8EOlMoaGxjDIrudZsmZXfx92DAxxP4I9hRIGyg7KZRr5YVp5sTgiekkyGgb/jTa26mJuEIFOSzi48ZoLn78EEoJFDlRsvYkq7eCpBXKYLa0lnUFtjbLDCqGmglthelUkIzUkwy7i8Sg6M3F0xl10pWPoyiaYpxja2+mXCiMdjSKdSpnVNkTGSETxV5Bopxag6neqWsWD3avnmXe18lUO80niIv2KFReFooP8GMnCbb9Tw2ClgBF2SyMFDyXE2HHTdmNeJZZaJI4w8xONsCy6caq/MKuT5ZHUGkntJUGg/7injExbhkNqEUhCPHIC1WfLDMqpwjXxJm+dkzC07+kRT3pEWCGaRjILHPCEy8p2vTAcHug14ppHW8zM9/DYEak1x6NKVKvngJhEqoYqSK2OI7oswut0jSYcmQ4jdqkF7WvUSpUX61r1vtJuLJ/Sj5GE5v18ZVddjh5QMpkNUetIPhx38VgDLh1pukGx2WcE5DN3LFoCSUhK8loS6OsiULqNLU/9+NERaCYov/VZDoopL1XeVBgf/oyLjgGDNYC0MeX3865ne8Irub+cmof79faQrerJsEK2/tMJ3MR98GUHYTESqHE1eoyhgokogVNXIDcdeQQJT/Xos8X6+PsSqC7TTQwrXGkMPRpBi4lBZLwHTroncBKeSc3Eo63vpkB5DfJ3SGdjW1RoOIECERiVL2EaBi9MwShrgZsE+fm7dkc/B7sZ/2TTHIVrBqbhc0PRNA0ktW3ErNbnu4WGmfJGH/rpvEZ+OpIumt5N/ZuOGLN1jYTt4q192Ews2i6s+ZgQvWkI07gWmkEgX+7aNJv9xwItohwazevCKPkGa+UWFiCaRyD9tQi05NGygVqYE1oEOgysLWffpJjqFrqFFIyEm4kWgVqYE1oEamFOaBqBPL3r7T+W0LKljz00AWrvwTV3MqVpBLIzof5BC/OE5ldA8whEDTTdbYLFCWnS6VyrhTTVBmp1XUsfLSO6hTmhuTbQItdBNvfspvSwv54snOoWeBdmbNDFakRb+8c/WIw4KO8BYerdQkfzK6B5GoiukY+wHktIuSwGehwS5kZknfyNxmw8mljD1EDHoAU0C60bwbNDEwnU2KgtGQ921s5SWoEfK1/9Pv/n1uhs3FPdRDo2rSO1MSbiOhTmnHkffh6biMYTyJ99tpV4NAXQG6O8zksxjiQcPVbKqCrVGorFCkaGRzE0OIxB3+l4ZGgUoyMlnvcYLgIHWhOaFxnbd/Z50JWq2orrolyooDBWRn64zDQKGKUbHh7DCP0KlRrKVYb0mDctR6Nl68wCorNLy0UcBQYdYwbLlFfZk9M3wgDHSdB3yjoAR4lJk7mN4OM0aPx7YSJQOILf+esf48aHIshlSAQzitG78bMQsCqcjNHzxp4bw1g1D7c8ivVdwMbeCFavXIZsVosFyEivwakA+dEKBocq2MftUwfKOFAsItXWiUQkyVgO/1ySjH3Hc1AoFOEUS+jOhHDaGR3YdFI3OtoiSMRC8ByXeXHx3EAee/Il7BmkI2FriQyS8bh5qyJsXs0+tBxVshjJ1h7Ok+A1VF0S0Zd9giRyQwn0J+IkM/2M9yxkNg30bvymSAU3v/UCrGhL2Zcc/LisDpwdDjcR3AQCsRmJQJ+5DTc+HD4KArFVh2souUUURodxwQk5XH3J8Xjl6b1Y393GADMrzbzr4MlnBnHPk334/A93YedgEgmtC+2fPxi2K8kXSog6w3jhxg5c9eLVuOTUZVi/qh1t4SkLRPmosox7Bkbxk139+PrDO3D3njw8ErYtHmH+Wf5DoOx4OC7l4O+veBFWaq1rn936TVE3bS2U8bs33I/hagwxxne0duQSINDtPoGSR0YgBsnnq8jGBvBHbzgNv3fRJmoSvYVVh6lZNdEGnjaNN37pR7juvio6MlnbmolgYQJDm5ALKhUMDw3hJScl8b8uPwmXb16PCPNuwTwbLpB+9dlWHHVCrVCL3Pj4bnzh9odx/xCQ7GhHjFpFq94fjBCKlTI2pir40bW/jq5IfcQWvyoU8Mqv3IFBN4lYVG9+HB2OFYFmbpzzAGU2P1bFisQQrvvABXjvy05BQhWqigx6h+nqZdxfPwrIuFix1hwLhMZuij9lxicKFd0ICkP78IEr1+AH/+fl+M1zjrfk0TXGiThTyCNIYzA/RtE4WsQuhKtOWodvvelluHpTNwrD1Eb6cMtM/aYkHo6x67IFUR4DZ8B2FtMqs5TFDDEsKCwIAgUrdo2VHKrxQfzL+8/DxSetoFCLdDw3VZI6nuqmwMY4cULHWrRVC+hVWHleYR++9I7N+NRV5yIT8nWUfqbGO50Tr7SVspIEGe/ytjT+4crz8K4zV9Lg1tLDVrQKNi1sBhc9FgSBJEw9Iloq5vHeK0/BxZtWsk5KlL6WCGYVSNjqYoMKJIbKNezPuxjR6nmCqdGJ4qhBmzWHjPNBotbcMopD+/GJq07GtZdsMvHaJXX9MEKwH6Q31ZnzfiBtmKwUisZOf/HKs/HGU3MojvYzanYWtaADnYzp/BYjFgaBiCqHxet7gbe9dIM5DnMIb97+VIUpl6okbv79jl/h9Z/8L1z6kR/hFR+9Ha/4s1tw5ed+gE/fsAUPPrnPD8zKpCGuVTfsoZZSEZEiGOwfxjUX9eC9l59Bb5GHjsHUvdF0sQTRNdw+MDCC727fjeseehLfevgp3PLkc+grcdhnzjNAHTeljNStScN98pXn4PT2GkbKJQZVlzl7KKxdd3pxwEq7oZAI6KY1Ig+GjHet91Mpl3H2hm6sydmV7A38KIzZwQr74+vuxJu+cC9ueiSCR/ensW0khQf2JHHzL2r48Ld245Uf+wneTHLduX0Pr9HSu6w8xiHlpRVWCqUyTl4Vxsfe9CLG6jJdDsMNQ0kAhgmyvP2Z/fjQjVtwxb/diWu+/xCu/cFDePsPHsYbvnMvXv5PP8aX7n+cRjIDyok1IhITUVQO4+hJJfHei89CpFw05826mUsUTSBQPWYjOal5aiAO24/r7fT96sBaVT09fmAAX/nxs0h0bUSuPYd0MoS2aAjtsTa0p7uQ7VgGL70M12/zcMVn7sGd28rIJBMIa30f1qxLFlbzfXjvqzdgpUaGXtmuz0NnvjbINML8uWPrLhLxJvzjfYMoJzoQSWeRzC1DPNOBcFs7HkUG7/7vrfjALT+nQW7LJxtOJFd5tZHva09aj5eubcMoSdt0Mc8jmlCyIErTLO3uIaBWG6Km0FpjiYQ/hK7rGoKDSoUhODLS5ycjrPxoTR/qrbDSSQRt2dK18le2PQsv3o0xh/aTogux+2LtFssOTlkZx+vO30jC0s/VeopBddt87Ng/ht/97H3YObYcmUwOURIjwe5EiyZEwlHE6DpjYXR09eCLD/bhU3dsZXGZiDSZWoHiYXyyvbTM+NVnH8fsj7J7PLwcFiua2DQotFkKTgNWWQp6EP9gmKaN45Z347QNWQ6Tn6WhoZaurCu8tW8sFejHOOLcRCNhoxUUs5bGcyoFvOLc9ViW0i0SnWDFB08LkD2K6RPffAC7RuLoybSx59G8lZ+6Tw590kBKJ8bjdEcX/u6+J/Hgvn5ernhsfkxe/PAXH7cKx2dCKDuy9G05lhp8CTYQVpcfESRuzcEc6OuzHkEUhoB01AJZGil//WYap2sd7B0eRDEYfY1DF9n2b2PkEYmhL+Po85mpmIeXv3CV8Rf9gltX0jzabtk5gOu3DCCl77ySdFoOz2pH/SiMfiwUe4IEHahG8ZX7njZdZNhQkMF40swBcbuOttDmVZ2oOOrGliYaTyBfzvrmhPkW6SwgocfjMdy3fT/2Fyjs8XtKE5UGz8EL1/fgP//kcrznkhxi1X0YHK7AdWLUCKxA2jrkmVEW6k7GuxQypczua3VXDKevWWb96MxZk4QN98NfPIfBMtOldqvWzXwbLjCMuqX6Px0n4wncQeLtL9FYNp9IrAMPlc6m5R2MQB9jWppoGoEsZie2cMhBOB7HQ3sTuO6nO+VDF1QIIzTNWnYSidCewufefhFu/shFeMP5GZJjAHtGSjRo1RHyGqMB6wjAii27ZazpTqA7HTfazLyYbLKmWrZ53LK9D9FYkopGaU/Ot45Eocl/HiLJGPaOlfHMYIEhpr+Dvrarg5xUOpMEs2TQeALVQzdG/d2ZIHvB2A0MmGjrwBev34YnDowawug7Y6b6FAmd6SY0GcTu5TwO+f/t98/HD/7kXPzWC2Moj/ZjoFKBF5XtYu0XwYnoMwFFdJN4ZhnyIEPScvogLzHseNgzRK9wFhE3ZL4LL/ibGaE78JVaGjv689ZDw7hxZ71WZzKI6fNQjMyu3xhkYGmgeQSinIz9cBjYLkI6w0VHrIqnR+N45xdvx4FCiWdjpEyNZ3ytIMOF/x7tJfGIYzNcuHEFvvP+l+Gr7zkHJy0rY2BgiKTUZzA1OrKzwC67kAxHeNPnJoyhQhHDdPq4rx3S080i7/o8Q5k5GSzObOO0xUJImrWnD8PGRYrmEchU3WyEpopSNsKosoVmc1nc+XgIV/zlrXj4mWH62iX/bRhpJbZkOuvHbsPVguMurjpnA2778KW4cnMX9vcPw62yk9ENUTepmkb8ID7QQ99XZ7z6nnuoViRp9PFe6kONtnQvy1T6zETSlz2qXh76lOVkTJRbnBchlypUK02BFuw+EsiCkf0Ro43S0dmDu3el8OqP34a/uWUrClXpG9UEK5PGsuZdxuuEfuoeqqzO3mwaX3/fJXjrpd3oH9rHc7RT9IYsiTmuULQTOJ8cEZImqjCMVNEaM2oW0ANtIRLNfE5zBjguTW6WSRpNfAyM+6WCphEorAm2WVZEANWpp09hs2PIdITRV2vDH35tG175kf/Gt+55iiSR3SJjtb4ybOWIrrqvlWAcX7j21/CazVnkh2lL0VqWRpEzqK9AP3/ZdBrt2XbTLWoIHpbmmgUUVp9HyKTqv3nqw09nJK9vwNsOdymiCQRiJQtHSJ4AskBkesdp32YjCbR3LsO9z4ZxzZd+iVd8/Fbc9MAuxq2nFpV1OWkkXsXKNEmyO2qjNvn4G19Em2qMRq6CxzDI0ZKFtJeuodMIjNosSxKszaXgVKskj+6f2cyLAtrTiG2q06Wya9JRDyf05Ez4cfCc/4M9wyMoMZajFMeCRxMIVA8rxCODFbVMXs386tsT7Zk00tku3P5EDa//7H246tO34rE9w0bzuLSD9PCYKtTWuL3+BWs7cf4LujBK4zgWTWDH3jyG9dT6QVVp83juxiyc4gCqJIX5+p9IyrgOrnjro3muatXB+nQCGzuzxm8cCuJfuH3/CBzzGfKjkcXCR9MIFEznHy1qpgL9ORse6zMDPZkkUrlV+PZ9BbzqI7fgx4/tMjPYgUltwXT9WyJnndABt5JHIprErn0FPNnXz4z5RZ5gnDn89RetRU82jAKNcs/cY7N2k5SRIdK484+pAUvlKl52fA864+pWfc0bgGH0xsVDe4eBuB5NsSnp2sMjyNt0CGJaGGieBmIF2aHr4QuroXOEwxW3GqGNzFEXu5Ega/ZONytHWoHhErSElvW0Y1c1h/f8wxb0D3P0FOFIrH7W29/vbkuSWvran4P9xRDu2E4CEXYiUNqI+TPZ83Dqik5c9WsrUDwwQPstxkGAvt3MQbo0kt8YpEXCoarJ02ixiFVRB288O/gGfl2Fm90aHj0whG3UfCk9nM906kIcAgpVT8Z6wmgruUx1wfljD6XeFLCNzk5iDFOrReE5HpZnXI7CRjDKlq0T0kISpRwH2nDZnanaddze3oltAxE88swQj+rfngiErBG5OkL+1RxEkyn81z27TRdl1IgJV48aPvT6zXjROg8jA4PUMKJLmWlJ6yg+zT3rwbMoio6D6sg+fOjCTTijK2eMdxjS+zBRh/H9rTvQX4sgZuagpqY3A6QZx7WjjpmqGg8NrxAHGKEwZaPpBvMpddlxcsrZNPCjqNtpOJpGoNlCRauwgoYKZVx+Tjf+/n+ejONyIzjQfwDlkuPLiENlj0NtyinKC+IkgL4tl4lW0NuV9GM5uILKZdGtYjRGKhnHTx8awK2PPEtCsEL1eXF98lITNca5WEND+l8+eCFO7h3B3v4RlCtJ86iqtJAeFNMbGAfyBUSG+vEXLzsd73rhqSS0CyeQojG+lZcwdhdL+M4v9yKWbjMVP1voasdPzziW26VzKAPtm2Npa6alfT2Mp2us4a+96aBzsyTwEaLxBPLLENH3G01Ln6lQFtbOoYBZUxF2PVecvR4//thl+NMrV2J1fACjo0MYHB02M8UjpTIG83kcGBhCdWgXPnjFJpy6spN176cx3nJtsZ7aNWQErD/pgGKsG391/SPIe/YWhumaAtnq1onn4rS13fjex34T7zqvFyeEPSRoQ0XHhhDLD6G9OoZXrorgG2+8AB88/0wS29wRs6mZtMlwPyv/evdjeDIfof0VNw/zm1GiqeRDQ09GdjgjyFaH0OaMGpeiS1SGEK9om0eyzG6xzHOVEZ4rICoNOSvbqvFownthjIcG5ju/fDf+9c4iujNZmK82H+K9MI+aID80hPdevgaffOMLfF/gmZESfrr1OWz51XN4arCMAY7JU14Za9ti+PUXn4jXnruBGZcOoIZizVnDnY7JDFY9XPS/v4/tw1lkk/rKIK0e2jbDgwP4+G9vwIdfewaD6kaJjHV7mYF6A783eqZcwe7BEbgldRuaJW/Hpo60eVjM3G4Lmp/pQqgNeKwpiDue2Yerv34nyuleqkxNS9BfWo7nxhy9F+bgljdfihUJ5lqX+jAvVLIr3047TFpZXbbRL/bf7vu/8kmS9J/7+WP49uPDaEu100cNw8rYvBfG7u7mt52PFbQFA00laG+2dDuc0X8MCNROAqkfmplAqrH80Ajec/lq/OXVqlh6TQlqOyP7hfWg3ozhamrAVqBx0gS0Uz7/o1/hvV/bjvZcNyIcWZlIOWKr0lYJF/bhq+8+E6/dfJzxlgkzHichWklZmIfyp8KkqS2df9oY+YS6xkeHR0me27Dda0MnB2d6kCNMG80ODA5DIL8IE1Aih0IIf/jD+/G3D+1DLt3OQ0nJRnCsCFQvt4bCyNeobO3VSWla2NY5UUT2/dxXxUok8tXDHCkeGHOCHtIoMnDZbOmhYTS3So7kueeZAXz6m9uQjOfY8pm28Wf8jDBJIZfo/86//Rlu+tmvzDk29AmYKD36MSFNB/CfNrjNiElb98r8C0yjC+aMIvgltehbv3krto8l0BHJmEZpSmbkYGH4za2uFLQfOHkae5y7plemjTPuFEfgAj8FUWBGar7NOg9oGoEMHYzgWDBfxc8EPc6hbigW1c1NebjGWFY9WWoJ3FMjtg3Z+KmSTRJigGamWYl3P9WHaz5zF/rLYbQnXNoU0nw+GEwPuWUSUeRjK/Gmf3gUH73+F2a+xiREp/gUpfJv/TQCInNEKDnmVc9f14wfmaVbNgx7/bYduJKa5+GROLpSGRaZuqemh/rpTCYtVM16fch66Ujs9B3jlz0o0qvBjLNNMMzyndGCdAEMeSTjqagLM2m/cZCIGgu/dcomMfp5QnYzIkRFH47F8PTeMQyMFenDSpHGMOdmgs7I4rAhZGR/6pZf4spP3oad+9uQ1deiVSnG9rCQ1lLFadidSpGsqRX4+HV78BufuBU3PriDQ3yj0wiRQtf5QpenymUc/ZQ3ap0KmXDXjj14y/fuxLXffRB7S53oSOdIEKUjm0yJmYtNNIKiiNAvHVP8PNC9vcDJkDeO54zjeV2qoHqPfqqTtwg8zh3rN44ph81A420g8xPC7335LvzT7QV0d8gGUgubPn5Vqu26YnDyg1jXVcXLz16Di0/rxpnHL0dPWwKJmCp0MiqOg5FiFY/u3I87H9uD797zHB5/jkP9tnZqsjhJogphzEzb2GBToMqNshuQ+T1UHkG0mseFJ/fiNef04OJTe3HcyizTrZtf0sjNVKp/zOt2DebxG//4bTxSzaKzuwtpdqUVdqF6S0RFCmbIJ+yIEMrVCtYkq/jM5WdiGTnsOhof+meDHZHOaBWB+9wdP+dDhzHm57P3PoVvPtFHIzplJBlg8a7Ooazx//e/fCe+bAjUwQZi1bM5MQ2ClPWN90rVRbmUN5/sX56LY11nAsuWtSPbHkE0TruH0YwMF7F/yMGz/RUcGC5gjPUV4yikPZ1mgfRsNGPUHA9R053P8RQmw9YRfygkPTtUKJdRLQwzHhcnnNKOdSe0Yz2HXO8+7zSsy2WYQWlUlc9ewx/csecA3vnNn+IJdlfZtjS1Sw1xV08UqBOaSiCB19KoCleLjIYaj+keLJWpPgeHCODFOTCIKA6lNRGuuGgJ5Cf4B/90F7586xg6RSCSR9+Nry/gdDD1bgoqMdMaYIW5jguH9oTjVE3csquitHci7PLCVPtxClDH6i5lSAZxBA3Y9CCzhM17GCOapexh99DrYmx0EJetyeHrV1+GHr0vpFd0ZHMprMdwkRQe6B/EO759Fx4diiGXzXI4zy45mKAcj9eHMsZMaYEHI/Igo0eJKG0xFb++qxaOFYEmp9oABOKImaGNHqayL93NhqA2r9aq0Ut9Sar4tlSEGiGDZZleLMt2YBk1QS6bRoYn9Yy8wplHM5hyfRIizpGQRzDEYzzpJM2jtIckE+joWY0fHQjjD753D8akgaIRWxYStsbuqkZCbe7uxDfecBFeknNxYGQYlYhGhTMk7mdKJlCM2jHGw8DFKbMjdXpi4YgL2kA0nEATkGWngqlWg/3DwwymxCT+68qgrehBM48jIiOrcWfPNRKG7K7mZ0hkJham7ZNub8O3njiAD37/Pv/5IpuBmnloLsKezcPGXDu+9j8uxatXJZHv3wdGMV6vdbyeBJWz3h0dbF7mC40nUCAtCcUUTOSRmz0mZOnvyH4yb2iYgymusTCKnnaJnko0yo3ETbgVLGvvxD8/sgt/etsWJmuJI4LZBwfsTd412SS+evWFeP2JnegfGkCFGkYmWP0wfqmh8QTy+3S94WBalmmtxEzNcNaYn0pQI+D4EEk9Ztu1Ep+/72l87v5fmuzUTxCqfJrU66Jt9s+/dQnefHIOhf0HOCrTKT1u4ofzMVX7yC1GNK0Lk+CNlO2BpG33FyiMWeO7SeCxlpYREeIc2cWzvfjIbY/iG1ufMNLTINw4njfGLJFho/naay/GR89fhxhHlFWNkMxkqiKXjpv+Lzh/ZG5+0fBRmIii2ZU//H9b8IX/HODwO226Ac02Uy/ZMAsQenMigKYTwys4ullB+4YGul6Ptq2A1UwulCphtLsDeOvm9eiNUD+NX2painEasFWiNfzbw33YVdQx5eJrmYbJ+hAYnwd6G0dh6UU0jB8n0L/fjy/8wCeQGcaLQDIYFiYOTaCA+LqNIRolUPaqqBRHdCGPA4IRlKN5AC3imVeFYsmckUnMTETOttrmDnsztYyb335hUwnkK91GYiKThpTGTphtdhc2pImsNqogwhFhJpVBeyqHXKqdLkvXxmO5lNkmkmleVDVrGTWoeR4BrNytKdE8NJxA44LSPAmdbXVN4Ok8IbgxroWuahp9USW5JJNxVLAujSGPTi1XtlGMAjHVeAy1z7FE8zQQY7a9opKQ3+IV4Hju/Z2gXRtO6K3UwNGKDvnbCY3jX7REodptKILBll73nWh0i1eIJufSItM586ftZLcwIFuMmyZnqOEEolwN9F6V2Q88FjU0ADhSN78wYjd10FwGNZxAAYI3O5cIgxYlzMuR/n6z0DQCTWifZhehuVDup3OCujEN6xcqzBI1QWabhKYRyGZcP4FKb3JJWpiEQPzN1kHNI1A9pnkisIVjAdKnye224QSSWrdQzuV8jykPPC0WKPfTuXrYO+6T3XxDWdACEHo9/OAcNw6NJ5C/9Rh18B2KFuYH9kmToCE3py6aphY8r0Le64/8Nw1gATTL5xVod9YqSPiP00z0DI1F0wjUHqcOckuWQoZBi1MbKee6dTHV1UOHU918QjdOHaeC7mwIKRFIj6M3CU0gkKX6xWdtQGfShetoKqtpPD0mMCSa4hYm7Ny4F4rAKxfwqlM2IMouTOan/yJrw9HgmqW+YYb1CMR5G3rx+pd0Yqi/n32x3tGa/9nZpQ4R2wtHMVpycHZvDFedtNL4ikAyhcadCd0YNEE12DkfZfKPr34xXrAxjH3FPoS1uM8ihcoy1S1EeJEahr0yupwB/Pll56A7mW66tmwCgZhl0lzd7qpsEl97/4U4rrOKvoEC/e0DZeYxoYXbD0yCIYyKNMXVQ4dT3bGGhuzFsofEyBA+9fIX4OXrV7Ap28/CNBMNJpBtn0Er1QoTZy7vwI0fvIzbEvYP7DUf2ndrE+9WhQyT5kPkixuSmL5Nrzdva+y2hkfHsMwZxteuOBdvOetEnpd8Ldkl43rXSDA+G2PDHmn1IVvI3AvW6hihOJ4bzuP9X7sH1/28H8l0j/mGhCEaw9n5Irn5w0yPtHq052a78PixhtE6VPWlkRFs7vDw2StegvNWdrHl6qTqwN8Zx5HL+HAPwjWNQOrG9O63tYnotCYhC/Cvtz2GT373MTxxIGpWh4/H9PDV/JJHmIlAtRkIVC+t6UY4TZ2Npmyl3UeKZSyrOnjLmT34wMWnoydJE4HDd9RSJgN2XYDpoAzPTubzRqDxhFkBtsD63qksoyh2D+bx+Vt+hW/c8TT25mNIt3Ugnohbwim8mhAvH1/dwvw2FwuNQGauiXFoq/j1LLbWT6y6HgqlInKhEi47cQXe96JNOHel/Vix41Yoaq01qVGvH8e00IkZT07C/BPIR338wbnH9gzhq/+1Fd/Z0o8dwy6i8TQSJJLmLqJaupdDf7O+4jHAgtNAjFPx6hVpJxSGU3ZRKxTQE/bwqk1dePNZJ+Cla3v8gNzUi8kYPtqxmdBaRZOhkybAYbFgCCQojXF/Jefv7h7I4zs/3Y5v3r8XD+1wUC6HkWxLmi8Iikz6vNNEgRubzwBzIVAjYSxHFpX6BuVKhSOrMmK0I0/qCuM1G1fgdacch9ODz6NrrRszDpoq6/rcsStbcgRSUuqtuBsy770rLA1CdmH3PfYcbrlvD+7YdgCPPFNA3o1SK0kzRRGhvaTXibWwruKZtDq9EcjRl6FZBFIZZeOZAYUcL1R3pCM7uap827NVz6NRXIHnVJGCg/VtUZy3KodLN67CJSesQk9cdiQj0LtovhyZUxvFpAzVHyxSAh0KKk99/2zzODmjWqb3/qf24icP7cMDj+/H9j2D2DtCkrkxRJNpaid2cjHaTeaRBYqfBNC6ilqTWfVthrFHgHoCqXLCK0ij5Yybrdwu0TszDpeSyR9DWQKRlGwCIr8Wj9VKazW3RC3rcWTq4rhcEptXd+Plx63AOau6sDpd9ykp1pEt12RZTeVHvWznigVJoKk4OJOThaTpsKf2j+KRXw3gwR0H8OCuPmzfW8J+EqrghPRpFPPR3nQ4jRhtJ/MqdUSvU9u47SMNPrhrJhD4b8psS29uQCqUFrCyBOL1ItAMGiiAyTudiZPQIp6KV3EE0deoMTyOlhySRaveh3ncFvXQkQhjbTqMk3tz2LymC6fRpjmjI4e2+o/1KT6pa39AcayxCAlk8yGRaU9bKW4LG871HPSNOnh8dx8e7xvDY7v248nBKnbvq2D/aBUjpTDtBw8OySDNIi0VYaUoHTnFazoVVTT3zcfxDGzaeghLGii+NmnC6DMLStqmrt8J/aY9TwtPKS5XH8HUVrn2aL/Z18XSkTi6WIjV2RCOy8axcVkPTujN4MRlWWzItZH4U8gh08bAz49+Dq0Em4ZFSCArsPrc6KyqXVk8VIEGS1UM5/PYSzI9tW8Iu4YL7PaqHN7SIC1x+Gs+4qJvaFRQrbioVl12IXZdxqq0g1uDQ+0wRiK4nQlEutMosjZFCI/nXJLRZZdmVqhmNmKs+HQygVg8jHb2nd2xENKZLLo70libqGFFOoZ11ChrOtrQnUqiIxW3X46eBvYb+9J+5Mq4AKQVbXkb2S0dCRYlgY4FVF0OuyetLiYNIsNZIlA9lkioArdlVmCRhoq+ZOg4lmTqhsQe2WCZSBS5VBpJEoNcYdcTQiykj8vNAJMAnco7tcz0Vw3Id/5q4mA87wikagjeydcIRca1gZIwySiETc9WWNDGhXoZBPuz7Tukl2R3aetrE4+souqQX/1K8irvkZZ5vupnURCokVBlGTXCgttlVwLY8k0uJSvSr2D5B7RSdxGEk5/xV7TaGQ9ofsyh8Q/2/T15mbjpURd80eF5R6AWGovDEWiebPsWlgpaBGphTmgRqIU5oUWgFuaEFoFamBNaBGphTmgRqIU5oUWgFuaEFoFamBNaBGphTmgRqIU5oUWgFuaE8ZupLbRwNDAEcl0X5XL5iJ9RaWHpQnpFjwEnEjM9Q2lhCFSpVDA2NtYiUAvjEIFisRgymYzvMz3GNVCpVGoRqIVxiEDSQMlk0veZDsD/B107UcoXqiJgAAAAAElFTkSuQmCC"}